"""
Quotation Template service module.
This module provides business logic for quotation template operations.
"""
from typing import List, Dict, Optional
from app.repositories.quotation_template_repository import QuotationTemplateRepository
from app.schemas.quotation_template_schema import quotation_template_schema, quotation_templates_schema
from app.utils.firebase import download_file_from_storage, upload_file_to_storage
from app.services.document_service import DocumentService
from app.repositories.customer_repository import CustomerRepository
import logging
import tempfile
import os
from datetime import datetime
import io
from docxtpl import DocxTemplate

logger = logging.getLogger(__name__)

class QuotationTemplateService:
    """Service for quotation template operations."""

    def __init__(self):
        """Initialize the service with repositories."""
        self.template_repo = QuotationTemplateRepository()
        self.customer_repo = CustomerRepository()
        self.document_service = DocumentService()

    def get_all_templates(self, active_only: bool = True) -> List[Dict]:
        """
        Get all quotation templates.

        Args:
            active_only: Whether to return only active templates

        Returns:
            List of quotation template data
        """
        templates = self.template_repo.get_all(active_only)
        return quotation_templates_schema.dump(templates)

    def get_template_by_id(self, template_id: int) -> Optional[Dict]:
        """
        Get a quotation template by ID.

        Args:
            template_id: Template ID

        Returns:
            Quotation template data or None if not found
        """
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None
        return quotation_template_schema.dump(template)

    def get_default_template(self) -> Optional[Dict]:
        """
        Get the default quotation template.

        Returns:
            Default quotation template data or None if not found
        """
        template = self.template_repo.get_default()
        if not template:
            return None
        return quotation_template_schema.dump(template)

    def create_template(self, template_data: Dict, file) -> Dict:
        """
        Create a new quotation template.

        Args:
            template_data: Template data
            file: Template file

        Returns:
            Created quotation template data
        """
        # Validate with schema
        errors = quotation_template_schema.validate(template_data)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Create the template
        template = self.template_repo.create(template_data, file)
        return quotation_template_schema.dump(template)

    def update_template(self, template_id: int, template_data: Dict, file=None) -> Optional[Dict]:
        """
        Update a quotation template.

        Args:
            template_id: Template ID
            template_data: Updated template data
            file: New template file (optional)

        Returns:
            Updated quotation template data or None if not found
        """
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None

        # Validate with schema (partial update)
        errors = quotation_template_schema.validate(template_data, partial=True)
        if errors:
            raise Exception(f"Validation failed: {errors}")

        # Update the template
        updated_template = self.template_repo.update(template, template_data, file)
        return quotation_template_schema.dump(updated_template)

    def delete_template(self, template_id: int) -> bool:
        """
        Delete a quotation template.

        Args:
            template_id: Template ID

        Returns:
            True if deleted, False if not found
        """
        return self.template_repo.delete(template_id)

    def set_default_template(self, template_id: int) -> bool:
        """
        Set a template as the default.

        Args:
            template_id: Template ID

        Returns:
            True if successful, False if template not found
        """
        return self.template_repo.set_default(template_id)

    def get_template_file_url(self, template_id: int) -> Optional[str]:
        """
        Get the download URL for a template file.

        Args:
            template_id: Template ID

        Returns:
            File URL or None if template not found
        """
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None

        try:
            # For now, return the API endpoint URL for downloading
            # In a full implementation, you could generate a signed URL
            return f"/api/quotation-templates/{template_id}/download"
        except Exception as e:
            logger.error(f"Failed to get file URL for template {template_id}: {str(e)}")
            return None

    def download_template_content(self, template_id: int) -> Optional[bytes]:
        """
        Download the content of a template file.

        Args:
            template_id: Template ID

        Returns:
            File content as bytes or None if template not found
        """
        template = self.template_repo.get_by_id(template_id)
        if not template:
            return None

        try:
            file_content, _ = download_file_from_storage(template.file_path)
            return file_content
        except Exception as e:
            logger.error(f"Failed to download template content for template {template_id}: {str(e)}")
            return None

    def generate_quotation_document(self, quotation_data: Dict, template_id: Optional[int] = None) -> Dict:
        """
        Generate a DOCX document from a quotation using a template.

        Args:
            quotation_data: Complete quotation data including items
            template_id: Template ID (uses default if not provided)

        Returns:
            Document data with file information
        """
        try:
            # Get template
            if template_id:
                template = self.template_repo.get_by_id(template_id)
                if not template:
                    raise Exception(f"Template with ID {template_id} not found")
            else:
                template = self.template_repo.get_default()
                if not template:
                    raise Exception("No default quotation template found")

            # Download template content
            template_content = self.download_template_content(template.id)
            if not template_content:
                raise Exception("Failed to download template content")

            # Prepare template data
            template_data = self._prepare_quotation_template_data(quotation_data)

            # Process template with docxtemplater
            processed_content = self._process_docx_template(template_content, template_data)

            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            customer_name = quotation_data.get('customer_name', 'Unknown').replace(' ', '_')
            quotation_number = quotation_data.get('quotation_number', 'QUOTE')
            filename = f"Offerte_{quotation_number}_{customer_name}_{timestamp}.docx"

            # Upload processed document to storage
            file_path = f"quotations/{filename}"

            # Create a file-like object from bytes
            file_obj = io.BytesIO(processed_content)
            file_obj.filename = filename
            file_obj.seek(0)

            # Upload to Firebase Storage
            file_url, actual_storage_path = upload_file_to_storage(file_obj, file_path)

            # Create document record
            document = self.document_service.create_document_from_bytes(
                customer_id=quotation_data.get('customer_id'),
                event_id=None,
                file_content=processed_content,
                filename=filename,
                document_type='offerte',
                uploaded_by=quotation_data.get('created_by'),
                file_url=file_url,
                storage_path=actual_storage_path
            )

            logger.info(f"Generated quotation document: {filename}")
            return document

        except Exception as e:
            logger.error(f"Failed to generate quotation document: {str(e)}")
            raise Exception(f"Failed to generate quotation document: {str(e)}")

    def _prepare_quotation_template_data(self, quotation_data: Dict) -> Dict:
        """
        Prepare data for template processing.

        Args:
            quotation_data: Raw quotation data

        Returns:
            Processed template data
        """
        # Get customer data
        customer = None
        if quotation_data.get('customer_id'):
            customer = self.customer_repo.get_by_id(quotation_data['customer_id'])

        # Prepare basic data
        template_data = {
            # Quotation info
            'offerte_nummer': quotation_data.get('quotation_number', ''),
            'titel': quotation_data.get('title', ''),
            'datum': datetime.now().strftime('%d-%m-%Y'),
            'geldig_tot': quotation_data.get('valid_until', ''),
            'introductie': quotation_data.get('introduction', ''),
            'conclusie': quotation_data.get('conclusion', ''),

            # Customer info
            'klant_naam': customer.name if customer else quotation_data.get('customer_name', ''),
            'klant_email': customer.email if customer else '',
            'klant_adres': customer.address if customer else '',
            'klant_telefoon': customer.phone if customer else '',

            # Financial data
            'subtotaal': f"€ {quotation_data.get('subtotal', 0):.2f}",
            'korting_percentage': quotation_data.get('discount_percentage', 0),
            'korting_bedrag': f"€ {quotation_data.get('discount_amount', 0):.2f}",
            'totaal_excl_btw': f"€ {quotation_data.get('total_excl_vat', 0):.2f}",
            'btw_percentage': quotation_data.get('vat_percentage', 21),
            'btw_bedrag': f"€ {quotation_data.get('vat_amount', 0):.2f}",
            'totaal_incl_btw': f"€ {quotation_data.get('total_incl_vat', 0):.2f}",

            # Company info (you can customize these)
            'bedrijf_naam': 'AMSPM',
            'bedrijf_adres': 'Bedrijfsadres hier',
            'bedrijf_telefoon': 'Telefoonnummer hier',
            'bedrijf_email': '<EMAIL>',
        }

        # Prepare products data for loop
        products = []
        for item in quotation_data.get('items', []):
            product = {
                'product_code': item.get('product_code', ''),
                'product_naam': item.get('product_name', item.get('description', '')),
                'omschrijving': item.get('description', ''),
                'aantal': str(item.get('quantity', 1)),
                'eenheidsprijs': f"€ {item.get('unit_price', 0):.2f}",
                'korting_percentage': item.get('discount_percentage', 0),
                'totaalprijs': f"€ {item.get('total_price', 0):.2f}"
            }
            products.append(product)

        template_data['producten'] = products

        return template_data

    def _process_docx_template(self, template_content: bytes, template_data: Dict) -> bytes:
        """
        Process DOCX template with data using docxtemplater-like functionality.

        Args:
            template_content: Template file content
            template_data: Data to fill in template

        Returns:
            Processed document content
        """
        try:
            # For now, we'll use a simple approach
            # In a full implementation, you would use python-docx-template or similar

            # Create a temporary file
            with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
                temp_file.write(template_content)
                temp_file.flush()

                # Here you would use a library like python-docx-template
                # For now, we'll return the original content
                # TODO: Implement actual template processing

                # Read the file back
                with open(temp_file.name, 'rb') as f:
                    processed_content = f.read()

                # Clean up
                os.unlink(temp_file.name)

                return processed_content

        except Exception as e:
            logger.error(f"Failed to process DOCX template: {str(e)}")
            raise Exception(f"Failed to process DOCX template: {str(e)}")
