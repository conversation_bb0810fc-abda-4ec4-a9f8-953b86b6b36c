#!/usr/bin/env python3
"""
Check what's in the quotation templates database.
"""

import os
import sys

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set environment variables for testing
os.environ['SECRET_KEY'] = 'test-secret-key-for-checking-db'
os.environ['DATABASE_URL'] = 'sqlite:///amspm.db'  # Adjust if needed

from app import create_app, db
from app.models.quotation_template import QuotationTemplate

def check_templates():
    """Check quotation templates in database."""
    app = create_app()
    
    with app.app_context():
        templates = QuotationTemplate.query.all()
        
        print(f"Found {len(templates)} quotation templates in database:")
        print("-" * 80)
        
        for template in templates:
            print(f"ID: {template.id}")
            print(f"Name: {template.name}")
            print(f"File Path: {template.file_path}")
            print(f"Is Active: {template.is_active}")
            print(f"Is Default: {template.is_default}")
            print(f"Created At: {template.created_at}")
            print("-" * 80)

if __name__ == "__main__":
    check_templates()
