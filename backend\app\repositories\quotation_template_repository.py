"""
Quotation Template repository module.
This module defines the repository for QuotationTemplate model operations.
"""
from typing import List, Optional, Dict
from app import db
from app.models.quotation_template import QuotationTemplate
from app.utils.firebase import upload_file_to_storage, delete_file_from_storage
from werkzeug.utils import secure_filename
import os
import logging

logger = logging.getLogger(__name__)

class QuotationTemplateRepository:
    """Repository for QuotationTemplate model."""

    def get_all(self, active_only: bool = True) -> List[QuotationTemplate]:
        """
        Get all quotation templates.

        Args:
            active_only: Whether to return only active templates

        Returns:
            List of quotation templates
        """
        query = QuotationTemplate.query
        if active_only:
            query = query.filter(QuotationTemplate.is_active == True)
        return query.order_by(QuotationTemplate.is_default.desc(), QuotationTemplate.name).all()

    def get_by_id(self, template_id: int) -> Optional[QuotationTemplate]:
        """
        Get a quotation template by ID.

        Args:
            template_id: Template ID

        Returns:
            QuotationTemplate or None if not found
        """
        return QuotationTemplate.query.get(template_id)

    def get_default(self) -> Optional[QuotationTemplate]:
        """
        Get the default quotation template.

        Returns:
            Default QuotationTemplate or None if not found
        """
        return QuotationTemplate.query.filter(
            QuotationTemplate.is_default == True,
            QuotationTemplate.is_active == True
        ).first()

    def create(self, template_data: Dict, file) -> QuotationTemplate:
        """
        Create a new quotation template.

        Args:
            template_data: Template data
            file: Template file

        Returns:
            Created quotation template
        """
        # Generate a secure filename
        original_filename = file.filename or f"quotation_template_{template_data.get('name', 'template')}.docx"
        filename = secure_filename(original_filename)
        
        # Ensure the filename has the correct extension
        if not filename.lower().endswith('.docx'):
            filename = f"{filename}.docx"

        # Upload file to Firebase Storage
        file_path = f"quotation_templates/{filename}"
        file_url = upload_file_to_storage(file, file_path)

        # If this is set as default, unset other defaults
        if template_data.get('is_default', False):
            self._unset_all_defaults()

        # Create the template record
        template = QuotationTemplate(
            name=template_data['name'],
            description=template_data.get('description'),
            file_path=file_path,
            file_type='docx',
            is_active=template_data.get('is_active', True),
            is_default=template_data.get('is_default', False),
            created_by=template_data['created_by']
        )

        db.session.add(template)
        db.session.commit()

        logger.info(f"Created quotation template: {template.name} (ID: {template.id})")
        return template

    def update(self, template: QuotationTemplate, template_data: Dict, file=None) -> QuotationTemplate:
        """
        Update a quotation template.

        Args:
            template: Template to update
            template_data: Updated template data
            file: New template file (optional)

        Returns:
            Updated quotation template
        """
        # If this is set as default, unset other defaults
        if template_data.get('is_default', False) and not template.is_default:
            self._unset_all_defaults()

        # Update template data
        if 'name' in template_data:
            template.name = template_data['name']
        if 'description' in template_data:
            template.description = template_data['description']
        if 'is_active' in template_data:
            template.is_active = template_data['is_active']
        if 'is_default' in template_data:
            template.is_default = template_data['is_default']

        # Handle file update
        if file:
            # Delete old file
            try:
                delete_file_from_storage(template.file_path)
            except Exception as e:
                logger.warning(f"Failed to delete old template file: {str(e)}")

            # Upload new file
            original_filename = file.filename or f"quotation_template_{template.name}.docx"
            filename = secure_filename(original_filename)
            
            if not filename.lower().endswith('.docx'):
                filename = f"{filename}.docx"

            file_path = f"quotation_templates/{filename}"
            file_url = upload_file_to_storage(file, file_path)
            template.file_path = file_path

        db.session.commit()

        logger.info(f"Updated quotation template: {template.name} (ID: {template.id})")
        return template

    def delete(self, template_id: int) -> bool:
        """
        Delete a quotation template.

        Args:
            template_id: Template ID

        Returns:
            True if deleted, False if not found
        """
        template = self.get_by_id(template_id)
        if not template:
            return False

        # Delete file from storage
        try:
            delete_file_from_storage(template.file_path)
        except Exception as e:
            logger.warning(f"Failed to delete template file: {str(e)}")

        # Delete from database
        db.session.delete(template)
        db.session.commit()

        logger.info(f"Deleted quotation template: {template.name} (ID: {template.id})")
        return True

    def set_default(self, template_id: int) -> bool:
        """
        Set a template as the default.

        Args:
            template_id: Template ID

        Returns:
            True if successful, False if template not found
        """
        template = self.get_by_id(template_id)
        if not template:
            return False

        # Unset all other defaults
        self._unset_all_defaults()

        # Set this template as default
        template.is_default = True
        template.is_active = True  # Ensure default template is active
        db.session.commit()

        logger.info(f"Set quotation template as default: {template.name} (ID: {template.id})")
        return True

    def _unset_all_defaults(self):
        """Unset all default flags."""
        QuotationTemplate.query.filter(QuotationTemplate.is_default == True).update(
            {'is_default': False}
        )
