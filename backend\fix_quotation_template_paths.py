#!/usr/bin/env python3
"""
Fix quotation template file paths to match Firebase Storage.
This script updates the database to use the correct UUID-based file paths.
"""

import os
import sys

# Set environment variables for the script
os.environ['SECRET_KEY'] = 'temp-secret-for-migration'
os.environ['DATABASE_URL'] = 'sqlite:///amspm.db'

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.quotation_template import QuotationTemplate

def fix_template_paths():
    """Fix quotation template file paths."""
    app = create_app()
    
    with app.app_context():
        templates = QuotationTemplate.query.all()
        
        print(f"Found {len(templates)} quotation templates to check...")
        
        # Based on the Firebase Storage screenshot, map old paths to new UUID paths
        path_mapping = {
            'quotation_templates/offerte_template.docx': 'quotation_templates/ef858af6-105e-41ff-8290-41bce0dc6071.docx',
            'quotation_templates/OFFERTEtest2.docx': 'quotation_templates/deda4718-373e-41a3-9d1f-113ab31472.docx'
        }
        
        updated_count = 0
        
        for template in templates:
            print(f"\nTemplate ID {template.id}: {template.name}")
            print(f"Current path: {template.file_path}")
            
            if template.file_path in path_mapping:
                new_path = path_mapping[template.file_path]
                print(f"Updating to: {new_path}")
                
                template.file_path = new_path
                updated_count += 1
            else:
                print("No mapping found - keeping current path")
        
        if updated_count > 0:
            print(f"\nCommitting {updated_count} updates to database...")
            db.session.commit()
            print("✅ Database updated successfully!")
        else:
            print("\nNo updates needed.")
        
        # Verify the updates
        print("\nVerifying updates:")
        templates = QuotationTemplate.query.all()
        for template in templates:
            print(f"Template {template.id}: {template.name} -> {template.file_path}")

if __name__ == "__main__":
    try:
        fix_template_paths()
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)
